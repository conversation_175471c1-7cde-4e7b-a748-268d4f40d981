@use "../settings/variables";

.u-text-truncate {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.u-text-weight-bold {
  font-weight: bold !important;
}

.u-text-weight-normal {
  font-weight: normal !important;
}

.u-text-color-primary {
  color: var(--text-primary-color) !important;
}

.u-text-color-secondary {
  color: var(--text-secondary-color) !important;
}

.u-text-color-success {
  color: var(--text-success-color) !important;
}

.u-text-monospace {
  font-family: variables.$monospace-fonts;
}

.u-text-align-center {
  text-align: center !important;
}

.u-text-align-left {
  text-align: left !important;
}

.u-text-line-clamp-2 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2 !important;
  overflow: hidden;
}

@each $name, $value in variables.$font-size {
  .u-text-size-#{$name} {
    font-size: $value;
  }
}
