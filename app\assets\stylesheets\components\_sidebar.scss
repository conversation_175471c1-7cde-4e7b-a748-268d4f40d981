@use "../tools/responsive";
@use "../settings/variables";

.c-sidebar {
  background: var(--sidebar-bg-color);
  z-index: variables.$sidebar-zindex;
}

@include responsive.media-query using ($breakpoint) {
  @if $breakpoint == "extra-wide" {
    .c-sidebar {
      width: 440px;
    }
  }

  @if $breakpoint == "wide" {
    .c-sidebar {
      width: 420px;
    }
  }

  @if $breakpoint == "extra-large" {
    .c-sidebar {
      width: 400px;
    }
  }

  @if $breakpoint == "large" {
    .c-sidebar {
      width: 380px;
    }
  }

  @if $breakpoint == "medium" {
    .c-sidebar {
      width: 360px;
    }
  }

  @if $breakpoint == "small" {
    .c-sidebar {
      width: 340px;
    }
  }

  @if $breakpoint == "extra-small" {
    .c-sidebar {
      position: fixed;
      bottom: 0;
      z-index: variables.$base-zindex - 1;
      height: 100%;
      width: 100%;
      visibility: hidden;
      opacity: 0;
    }

    .c-sidebar.is-expanded {
      z-index: variables.$expanded-sidebar-zindex;
      visibility: visible;
      height: 100%;
      opacity: 1;
    }
  }
}
