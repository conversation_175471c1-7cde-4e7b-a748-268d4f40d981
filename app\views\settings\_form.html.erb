<div id='turbo-settings'>
  <h2 class="o-flex o-flex--justify-between o-flex--align-center">
    <span><%= t("label.library") %></span>
    <%= render partial: "media_syncing/button", locals: {syncing: Media.syncing?} %>
  </h2>

  <%= form_with model: Setting, url: setting_path, method: :put, class: "c-form" do |form| %>
    <div class='c-form__field'>
      <%= form.label :media_path, t("field.media_path") %>
      <%= form.text_field :media_path, value: Setting.media_path, class: "c-input" %>
    </div>

    <div class='o-flex o-flex--align-end o-flex--justify-between'>
      <div class="c-form__field c-form__field--inline">
        <%= form.label :enable_media_listener, t("field.enable_media_listener") %>
        <%= form.check_box :enable_media_listener %>
      </div>
      <% if MediaListener.running? %>
        <div class="o-flex u-text-color-success">
          <%= icon_tag("check", size: "small") %>
          <span class="u-ml-tiny"><%= t("text.media_listener_running") %></span>
        </div>
      <% end %>
    </div>
    <small><%= t("text.enable_media_listener") %></small>

    <div class="c-form__field c-form__field--inline">
      <%= form.label :enable_parallel_media_sync, t("field.enable_parallel_media_sync"), disabled: BlackCandy.config.db_adapter == "sqlite" %>
      <%= form.check_box :enable_parallel_media_sync, disabled: BlackCandy.config.db_adapter == "sqlite" %>
    </div>
    <small><%= t("text.enable_parallel_media_sync") %></small>

    <div class='c-form__field c-form__field--submit'>
      <%= form.submit t("button.save"), class: "c-button c-button--primary c-button--full-width" %>
    </div>
  <% end %>

  <hr>

  <h2><%= t("label.integration") %></h2>
  <%= form_with model: Setting, url: setting_path, method: :put, class: "c-form" do |form| %>
    <div class='c-form__field'>
      <%= form.label :discogs_token, t("field.discogs_token") %>
      <%= form.text_field :discogs_token, value: Setting.discogs_token, class: "c-input" %>
    </div>
    <div class='c-form__field c-form__field--submit'>
      <%= form.submit t("button.save"), class: "c-button c-button--primary c-button--full-width" %>
    </div>
  <% end %>

  <hr>

  <h2><%= t("label.transcoding") %></h2>
  <p><%= t("text.transcoding") %></p>
  <%= form_with model: Setting, url: setting_path, method: :put, class: "c-form" do |form| %>
    <div class='c-form__field c-form__field--inline'>
      <%= form.label :allow_transcode_lossless, t("field.allow_transcode_lossless") %>
      <%= form.check_box :allow_transcode_lossless %>
    </div>
    <small><%= t("text.allow_transcode_lossless") %></small>

    <div class='c-form__field c-form__field--inline'>
      <%= form.label :transcode_bitrate, t("field.transcode_bitrate") %>
      <%= form.select :transcode_bitrate, Setting::AVAILABLE_BITRATE_OPTIONS.map { |option| ["#{option} kbps", option] }, selected: Setting.transcode_bitrate, class: "c-input" %>
    </div>
    <div class='c-form__field c-form__field--submit'>
      <%= form.submit t("button.save"), class: "c-button c-button--primary c-button--full-width" %>
    </div>
  <% end %>
</div>
