<div class='c-player u-h-100' data-controller='player media-session'>
  <div data-player-target='header' class='c-player__header u-position-relative'>
    <div class='u-position-relative u-overflow-hidden'>
      <div class='c-player__header__background' data-player-target='backgroundImage'></div>
      <div class='c-player__header__content c-card c-card--horizontal u-p-medium u-position-relative'>
        <img data-player-target='image' class='c-card__image u-image-small'>
        <div class='c-card__body'>
          <h1 class='c-card__body__title u-text-truncate' data-player-target='songName'></h1>
          <p class='c-card__body__text u-text-truncate' data-player-target='albumName'></p>
          <p class='c-card__body__text u-text-truncate' data-player-target='artistName'></p>
          <div class='c-card__body__text u-text-monospace u-mt-narrow'>
            <span class='u-text-color-primary' data-player-target='songTimer'></span>
            <span class='u-mx-narrow'>/</span>
            <span data-player-target='songDuration'></span>
          </div>
        </div>
      </div>
    </div>
    <div class='c-player__progress u-cursor-pointer'>
      <progress data-player-target='progress' data-action='click->player#seek' class='u-w-100' max='100'></progress>
    </div>
    <div data-player-target='loader' class='c-overlay u-display-none'>
      <%= loader_tag %>
    </div>
  </div>
  <div class='c-player__control'>
    <div class="c-player__control__main o-flex o-flex--justify-between">
      <button class='c-button c-button--icon' data-action='click->player#previous'>
        <%= icon_tag "rewind", size: "large", title: t("label.previous_song") %>
      </button>
      <button class='u-mx-medium c-button c-button--icon' data-action='click->player#play' data-player-target='playButton'>
        <%= icon_tag "play", size: "large", title: t("label.play_song") %>
      </button>
      <button class='u-mx-medium c-button c-button--icon u-display-none' data-action='click->player#pause' data-player-target='pauseButton'>
        <%= icon_tag "pause", size: "large", title: t("label.pause_song") %>
      </button>
      <button class='c-button c-button--icon' data-action='click->player#next'>
        <%= icon_tag "fast-forward", size: "large", title: t("label.next_song") %>
      </button>
    </div>
    <div class="c-player__control__secondary o-flex">
      <button class='c-button c-button--icon u-mr-tiny' data-action='click->player#nextMode'>
        <%= icon_tag "repeat", size: "small", title: t("label.no_repeat_mode"), data: {"player-target" => "modeButton"} %>
        <%= icon_tag "repeat", size: "small", active: true, title: t("label.repeat_mode"), data: {"player-target" => "modeButton"} %>
        <%= icon_tag "repeat-one", size: "small", active: true, title: t("label.single_mode"), data: {"player-target" => "modeButton"} %>
        <%= icon_tag "shuffle", size: "small", active: true, title: t("label.shuffle_mode"), data: {"player-target" => "modeButton"} %>
      </button>
      <div class="o-flex o-flex--align-center o-flex__item--grow-1">
        <button class='c-button c-button--icon' data-action='click->player#mute'>
          <%= icon_tag "volume-down", size: "small", title: t("label.toggle_favorite") %>
        </button>
        <input class="c-player__volume" min="0" max="1" type="range" step="0.01" autocomplete="off" data-player-target="volume" data-action="player#volume">
        <button class='c-button c-button--icon' data-action='click->player#maxVolume'>
          <%= icon_tag "volume-up", size: "small", title: t("label.toggle_favorite") %>
        </button>
      </div>
      <div data-action='turbo:submit-end->player#toggleFavorite' class="u-ml-tiny">
        <%= button_to "#", class: "c-button c-button--icon", disabled: true, form: {"data-player-target" => "favoriteButton"} do %>
          <%= icon_tag "heart", size: "small", title: t("label.toggle_favorite") %>
        <% end %>
        <%= button_to "#", class: "c-button c-button--icon", method: :delete, form_class: "u-display-none", form: {"data-player-target" => "unFavoriteButton"} do %>
          <%= icon_tag "heart", size: "small", emphasis: true, title: t("label.toggle_favorite") %>
        <% end %>
      </div>
    </div>
  </div>
  <div class='u-py-tiny u-bg-primary u-text-align-center u-display-none u-display-block@extra-small' data-action='click->player#collapse'>
    <%= icon_tag "expand-more", size: "large", title: t("label.collapse_player") %>
  </div>
</div>
