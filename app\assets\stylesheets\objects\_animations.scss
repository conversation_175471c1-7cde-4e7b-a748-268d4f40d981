@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translate3d(0, -100%, 0);
  }

  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes fadeOutUp {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
    transform: translate3d(0, -100%, 0);
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.o-animation-fadeInDown {
  animation-name: fadeInDown;
  animation-duration: 0.25s;
}

.o-animation-fadeOutUp {
  animation-name: fadeOutUp;
  animation-duration: 0.25s;
}

.o-animation-spin {
  animation: spin 0.9s linear infinite;
}
