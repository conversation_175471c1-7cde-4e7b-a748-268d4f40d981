<%= form_with scope: :session, url: sessions_path, class: "c-form", data: {controller: "form", action: "turbo:submit-end->form#reset"} do |form| %>
  <div class='c-form__field'>
    <%= form.label :email, t("field.email") %>
    <%= form.email_field :email, value: params[:email], autofocus: true, class: "c-input" %>
  </div>

  <div class='c-form__field'>
    <%= form.label :password, t("field.password") %>
    <%= form.password_field :password, class: "c-input" %>
  </div>

  <div class='c-form__field c-form__field--submit'>
    <%= form.submit t("button.login"), class: "c-button c-button--primary c-button--full-width" %>
  </div>
<% end %>
