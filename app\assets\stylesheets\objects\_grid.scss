/* Base on Raster grid subsystem (rsms.me/raster) */

@use "../settings/variables";
@use "../tools/responsive";
@use "../tools/functions" as *;

.o-grid {
  display: grid !important;
}

.o-grid > .o-grid__item {
  display: block;
  appearance: none;
}

.o-grid--shelf {
  grid-gap: spacing("medium");
  grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
}

.o-grid--list {
  grid-gap: spacing("small");
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
}

.o-grid > .o-grid__item--row {
  grid-column: 1 / -1;
}

@include responsive.media-query using ($breakpoint) {
  @if $breakpoint == "narrow" {
    .o-grid--shelf {
      grid-gap: spacing("small");
    }
  }

  @if $breakpoint == "extra-narrow" {
    .o-grid--shelf {
      grid-gap: spacing("narrow");
      grid-template-columns: repeat(2, 1fr);
    }
  }
}
