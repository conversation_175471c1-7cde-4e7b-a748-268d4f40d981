<% if params[:location] == 'last' %>
  <%= turbo_stream.after "#{dom_id(@playlist)}_#{dom_id(@playlist.songs.second_to_last)}", partial: 'current_playlist/songs/song', locals: { song: @song, playlist: @playlist } %>
<% else %>
  <% if @current_song_position.zero? %>
    <%= turbo_stream.before "#{dom_id(@playlist)}_#{dom_id(@playlist.songs.second)}", partial: 'current_playlist/songs/song', locals: { song: @song, playlist: @playlist, should_play: params[:should_play] } %>
  <% else %>
    <%# add song next to the current_song %>
    <%= turbo_stream.after "#{dom_id(@playlist)}_song_#{params[:current_song_id]}", partial: 'current_playlist/songs/song', locals: { song: @song, playlist: @playlist, should_play: params[:should_play] } %>
  <% end %>
<% end %>

<%= turbo_stream.update dom_id(@playlist, :songs_count), @playlist.songs.size %>
<%= render_flash %>
