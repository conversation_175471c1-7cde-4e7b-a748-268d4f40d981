@use "../tools/functions" as *;
@use "../tools/responsive";

.c-card {
  width: 100%;
}

.c-card--border {
  border: 1px solid var(--card-border-color);
  border-radius: border-radius("medium");
  background-color: var(--card-bg-color);
}

.c-card--border .c-card__body {
  padding: spacing("narrow");
}

.c-card__image {
  display: block;
  margin-bottom: spacing("narrow");
}

.c-card__image,
.c-card__image img {
  border-radius: border-radius("medium");
}

.c-card__body__title {
  margin-bottom: spacing("narrow");
  font-size: font-size("large");
}

.c-card__body__text {
  display: block;
  margin-bottom: spacing("tiny");
}

.c-card__body__text:last-child {
  margin-bottom: 0;
}

.c-card--horizontal {
  display: flex;

  .c-card__image {
    margin-right: spacing("medium");
  }

  .c-card__body {
    flex-grow: 1;
    width: 0;
  }
}

@include responsive.media-query using ($breakpoint) {
  @if $breakpoint == "narrow" {
    .c-card--center\@narrow {
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;

      .c-card__image {
        margin: 0 0 spacing("small");
      }

      .c-card__body {
        width: auto;
      }
    }
  }
}
