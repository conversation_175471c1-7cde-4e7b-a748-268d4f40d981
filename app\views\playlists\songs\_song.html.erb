<li id='<%= "#{dom_id(playlist)}_#{dom_id(song)}" %>' class='c-list__item u-p-0' data-songs-target='item' data-song-id='<%= song.id %>' draggable='true'>
  <div class='o-flex o-flex--align-center u-py-narrow'>
    <% unless mobile? %>
      <button class='c-button c-button--icon js-playlist-sortable-item-handle u-mr-narrow'><%= icon_tag "drag-indicator", size: "small" %></button>
    <% end %>

    <%= button_to(
          current_playlist_playlist_path(playlist, should_play: true, song_id: song.id),
          method: :put,
          class: "c-button c-button--link u-w-100",
          form_class: "o-flex__item--grow-1",
          form: {
            data: {
              "delegated-action" => "turbo:submit-start->songs#checkBeforePlay click->playlist-bridge#playBeginWith",
              "turbo-frame" => "turbo-playlist",
              "disabled-on-native" => "true"
            }
          }
        ) do %>
      <div class='o-flex o-flex--justify-between o-flex--align-center'>
        <div class='u-mr-narrow'>
          <span class='u-mb-tiny u-text-line-clamp-2'><%= song.name %></span>
          <% if native_app? %>
            <span class='c-list__item__subtext'><%= song.artist.name %></span>
          <% else %>
            <%= link_to song.artist.name, artist_path(song.artist), class: "c-list__item__subtext" %>
          <% end %>
        </div>
        <div class='u-text-monospace u-mr-narrow'><%= format_duration(song.duration) %></div>
      </div>
    <% end %>
    <details class='c-dropdown' data-controller='dropdown'>
      <summary class="c-button c-button--icon"><%= icon_tag "more-vertical", size: "small", title: t("label.more") %></summary>
      <div class='c-dropdown__menu' data-dropdown-target="menu">
        <%= button_to(
              t("button.play_now"),
              current_playlist_songs_path(song_id: song.id, should_play: true),
              form_class: "c-dropdown__item",
              form: {
                data: {
                  "turbo-frame" => "turbo-playlist",
                  "delegated-action" => "turbo:submit-start->songs#checkBeforePlay click->songs-bridge#playNow",
                  "disabled-on-native" => "true"
                }
              }
            ) %>
        <%= button_to(
              t("button.play_next"),
              current_playlist_songs_path(song_id: song.id),
              form_class: "c-dropdown__item",
              form: {
                data: {
                  "turbo-frame" => "turbo-playlist",
                  "delegated-action" => "turbo:submit-start->songs#checkBeforePlayNext click->songs-bridge#playNext",
                  "disabled-on-native" => "true"
                }
              }
            ) %>
        <%= button_to(
              t("button.play_last"),
              current_playlist_songs_path(song_id: song.id, location: "last"),
              form_class: "c-dropdown__item",
              form: {
                data: {
                  "turbo-frame" => "turbo-playlist",
                  "delegated-action" => "click->songs-bridge#playLast",
                  "disabled-on-native" => "true"
                }
              }
            ) %>
        <%= button_to(
              t("button.delete"),
              playlist_song_path(playlist, song),
              method: :delete,
              form_class: "c-dropdown__item"
            ) %>
        <%= link_to(
              t("label.add_to_playlist"),
              dialog_playlists_path(song_id: song.id, referer_url: current_url),
              data: {"turbo-frame" => ("turbo-dialog" unless native_app?)},
              class: "c-dropdown__item"
            ) %>
        <%= link_to(
              t("label.go_to_artist"),
              artist_path(song.artist),
              class: "c-dropdown__item"
            ) %>
      </div>
    </details>
  </div>
</li>
