// This file is auto-generated by ./bin/rails stimulus:manifest:update
// Run that command whenever you add a new controller or create them with
// ./bin/rails generate stimulus controllerName

import { application } from './application'

import DialogController from './dialog_controller.js'

import ElementController from './element_controller'

import Flash<PERSON>ontroller from './flash_controller.js'

import FormController from './form_controller.js'

import MediaSessionController from './media_session_controller.js'

import MiniPlayerController from './mini_player_controller.js'

import PlayerController from './player_controller.js'

import SongsController from './songs_controller.js'

import CurrentPlaylistSongsController from './current_playlist_songs_controller.js'

import PlaylistSortableController from './playlist_sortable_controller.js'

import SearchController from './search_controller.js'

import SongsBridgeController from './songs_bridge_controller.js'

import AlbumBridgeController from './album_bridge_controller.js'

import PlaylistBridgeController from './playlist_bridge_controller.js'

import FlashBridgeController from './flash_bridge_controller.js'

import ThemeController from './theme_controller.js'

import ThemeBridgeController from './theme_bridge_controller.js'

import DropdownController from './dropdown_controller.js'

import CoverImageController from './cover_image_controller.js'

application.register('dialog', DialogController)

application.register('element', ElementController)

application.register('flash', FlashController)

application.register('form', FormController)

application.register('media-session', MediaSessionController)

application.register('mini-player', MiniPlayerController)

application.register('player', PlayerController)

application.register('songs', SongsController)

application.register('current-playlist-songs', CurrentPlaylistSongsController)

application.register('playlist-sortable', PlaylistSortableController)

application.register('search', SearchController)

application.register('songs-bridge', SongsBridgeController)

application.register('album-bridge', AlbumBridgeController)

application.register('playlist-bridge', PlaylistBridgeController)

application.register('flash-bridge', FlashBridgeController)

application.register('theme', ThemeController)

application.register('theme-bridge', ThemeBridgeController)

application.register('dropdown', DropdownController)

application.register('cover-image', CoverImageController)
