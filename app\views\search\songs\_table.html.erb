<div role='table' class='c-table' data-controller='songs songs-bridge' cols-at-medium='3' cols-at-small='2'>
  <div role='rowgroup'>
    <div role='row'>
      <div role='columnheader'><%= t("field.name") %></div>
      <div role='columnheader' class='u-display-none@medium'><%= t("label.artist") %></div>
      <div role='columnheader' class='u-display-none@medium'><%= t("label.album") %></div>
      <div role='columnheader' class='u-display-none@small'><%= t("label.duration") %></div>
      <div role='columnheader'><%= t("label.actions") %></div>
    </div>
  </div>
  <div role='rowgroup'>
    <%= render partial: "songs/song", collection: songs %>
  </div>
</div>
