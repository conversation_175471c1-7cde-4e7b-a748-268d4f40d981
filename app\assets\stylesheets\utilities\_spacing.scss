@use "../settings/variables";
@use "../tools/responsive";
@use "../tools/functions" as *;

@mixin margin($name, $size) {
  .u-m-#{$name} {
    margin: $size !important;
  }

  .u-mt-#{$name} {
    margin-top: $size !important;
  }

  .u-mb-#{$name} {
    margin-bottom: $size !important;
  }

  .u-ml-#{$name} {
    margin-left: $size !important;
  }

  .u-mr-#{$name} {
    margin-right: $size !important;
  }

  .u-mx-#{$name} {
    margin-left: $size !important;
    margin-right: $size !important;
  }

  .u-my-#{$name} {
    margin-top: $size !important;
    margin-bottom: $size !important;
  }
}

@mixin padding($name, $size) {
  .u-p-#{$name} {
    padding: $size !important;
  }

  .u-pt-#{$name} {
    padding-top: $size !important;
  }

  .u-pb-#{$name} {
    padding-bottom: $size !important;
  }

  .u-pl-#{$name} {
    padding-left: $size !important;
  }

  .u-pr-#{$name} {
    padding-right: $size !important;
  }

  .u-px-#{$name} {
    padding-left: $size !important;
    padding-right: $size !important;
  }

  .u-py-#{$name} {
    padding-top: $size !important;
    padding-bottom: $size !important;
  }
}

@each $name, $value in variables.$spacing {
  @include margin($name, $value);
  @include padding($name, $value);
}

@include margin(0, 0);
@include margin(auto, auto);
@include padding(0, 0);

@include responsive.media-query using ($breakpoint) {
  @if $breakpoint == "small" {
    .u-p-small\@small {
      padding: spacing("small") !important;
    }
  }

  @if $breakpoint == "extra-narrow" {
    .u-p-narrow\@extra-narrow {
      padding: spacing("narrow") !important;
    }
  }
}
