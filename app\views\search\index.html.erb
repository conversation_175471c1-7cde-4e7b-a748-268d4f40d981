<% page_title_tag t("label.search_results") %>

<% if [@albums, @artists, @songs, @playlists].any?(&:present?) %>
  <h1><%= t("label.search_results_for", query: params[:query]) %></h1>

  <% if @albums.present? %>
    <div class='o-flex o-flex--justify-between o-flex--align-center u-mb-medium'>
      <h2 class='u-mb-0'><%= t("label.albums") %></h2>

      <% unless @is_all_albums %>
        <%= link_to t("label.see_all"), search_albums_path(query: params[:query]), class: "is-active" %>
      <% end %>
    </div>
    <div class='o-grid o-grid--shelf u-mb-large'>
      <%= render partial: "albums/album", collection: @albums %>
    </div>
  <% end %>

  <% if @artists.present? %>
    <div class='o-flex o-flex--justify-between o-flex--align-center u-mb-medium'>
      <h2 class='u-mb-0'><%= t("label.artists") %></h2>

      <% unless @is_all_artists %>
        <%= link_to t("label.see_all"), search_artists_path(query: params[:query]), class: "is-active" %>
      <% end %>
    </div>
    <div class='o-grid o-grid--shelf u-mb-large'>
      <%= render partial: "artists/artist", collection: @artists %>
    </div>
  <% end %>

  <% if @playlists.present? %>
    <div class='o-flex o-flex--justify-between o-flex--align-center u-mb-medium'>
      <h2 class='u-mb-0'><%= t("label.playlists") %></h2>

      <% unless @is_all_playlists %>
        <%= link_to t("label.see_all"), search_playlists_path(query: params[:query]), class: "is-active" %>
      <% end %>
    </div>
    <div class='o-grid o-grid--list u-mb-wide'>
      <%= render partial: "playlists/playlist", collection: @playlists %>
    </div>
  <% end %>

  <% if @songs.present? %>
    <div class='o-flex o-flex--justify-between o-flex--align-center u-mb-medium'>
      <h2 class='u-mb-0'><%= t("label.songs") %></h2>

      <% unless @is_all_songs %>
        <%= link_to t("label.see_all"), search_songs_path(query: params[:query]), class: "is-active" %>
      <% end %>
    </div>
    <%= render partial: "search/songs/table", locals: {songs: @songs} %>
  <% end %>
<% else %>
  <h1><%= t("label.no_search_results", query: params[:query]) %></h1>
<% end %>
