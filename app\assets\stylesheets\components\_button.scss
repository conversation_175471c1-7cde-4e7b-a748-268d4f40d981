@use "../tools/functions" as *;

.c-button {
  text-align: center;
  display: inline-block;
  border-radius: border-radius("medium");
  border: none;
  padding: spacing("tiny") spacing("narrow");
  cursor: pointer;
  font-size: font-size("medium");

  &:hover {
    text-decoration: none;
  }
}

.c-button--primary {
  color: var(--btn-color);
  background: var(--btn-primary-bg-color);

  &:hover {
    background: var(--btn-primary-hover-bg-color);
    color: var(--btn-color);
  }
}

.c-button--secondary {
  background: var(--btn-secondary-bg-color);
  color: var(--btn-secondary-color);

  &:hover {
    background: var(--btn-secondary-hover-bg-color);
    color: var(--btn-secondary-color);
  }
}

.c-button--link {
  text-align: left;
  background: none;
  color: var(--text-primary-color);
  padding: 0;

  &:hover {
    color: var(--link-active-color) !important;
  }
}

.c-button--small {
  padding: 2px spacing("tiny");
  font-size: font-size("small");
}

.c-button--outline {
  border-radius: border-radius("large");
  color: var(--btn-outline-color);
  border: 1px solid var(--btn-outline-border-color);
  background: var(--btn-outline-bg-color);

    &:hover {
      background: var(--btn-outline-hover-bg-color);
    }
}

.c-button--icon {
  display: inline-flex;
  padding: spacing("tiny");
  border-radius: border-radius("medium");
  color: currentcolor;

  &:hover {
    background: var(--btn-icon-hover-bg-color);
  }
}

.c-button--full-width {
  width: 100%;
}

.c-button[disabled] {
  opacity: 0.6;
}