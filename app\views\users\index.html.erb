<% page_title_tag t("label.manage_users") %>

<div class='o-container o-container--extra-narrow'>
  <div class='o-flex o-flex--justify-between o-flex--align-center'>
    <h2 class='u-mb-0'><%= t("label.users") %></h2>
    <%= link_to t("label.add"), new_user_path, class: "c-button c-button--primary" %>
  </div>
  <hr>
  <div class='u-position-relative'>
    <% if @users.present? %>
      <ul class='c-list c-list--border-none'>
        <% @users.each do |user| %>
          <li id='<%= dom_id(user) %>' class='c-list__item o-flex o-flex--justify-between o-flex--align-center'>
            <%= link_to edit_user_path(user), class: "o-flex o-flex--align-center" do %>
              <span class='u-mr-tiny'><%= avatar_tag user %></span>
              <%= user.email %>
            <% end %>
            <%= button_to user_path(user), method: :delete, class: "c-button c-button--icon" do %>
              <%= icon_tag "delete", size: "small", title: t("button.delete") %>
            <% end %>
          </li>
        <% end %>
      </ul>
    <% else %>
      <%= empty_alert_tag %>
    <% end %>
  </div>
</div>
