@use "../tools/responsive";
@use "../tools/functions" as *;

.c-table {
  display: table;
  width: 100%;
  table-layout: auto;
  text-align: left;
}

.c-table[role="table"] {
  display: grid;
  grid-template-columns: var(--grid-tc);
  --grid-tc: repeat(5, auto);
}

.c-table [role="rowgroup"] {
  display: contents;
}

.c-table [role="row"] {
  display: contents;
}

.c-table tr,
.c-table [role="columnheader"],
.c-table [role="cell"] {
  border-bottom: 1px solid var(--table-border-color);
}

.c-table [role="columnheader"] {
  font-weight: bold;
}

.c-table th,
.c-table td,
.c-table [role="columnheader"],
.c-table [role="cell"] {
  display: inline-flex;
  align-items: center;
  padding: spacing(narrow);
  text-align: left;
  color: var(--table-color);
}

.c-table [role="cell"][span="row"] {
  grid-column: 1 / -1;
}

@include responsive.media-query using ($breakpoint) {
  @if $breakpoint == "medium" {
    .c-table[role="table"][cols-at-medium="3"] { --grid-tc: repeat(3, auto); }
  }

  @if $breakpoint == "small" {
    .c-table[role="table"][cols-at-small="2"] { --grid-tc: repeat(2, auto); }
  }
}
