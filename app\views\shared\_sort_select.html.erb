<details class='c-dropdown' data-controller='dropdown'>
  <summary class="c-button c-button--icon">
    <%= icon_tag "sort", title: t("label.sort") %>
  </summary>
  <div class='c-dropdown__menu' data-dropdown-target="menu">
    <% option.values.each do |sort_value| %>
      <%= link_to url_for(controller: sort_controller, action: :index, **filter_sort_presenter.params(sort: sort_value)), class: "c-dropdown__item", data: {"turbo-action" => ("replace" if native_app?)} do %>
        <span class='o-flex o-flex--justify-between o-flex--align-center'>
          <%= t("field.#{sort_value}") %>
          <% if filter_sort_presenter.sort_value == sort_value || (filter_sort_presenter.sort_value.blank? && sort_value == option.default.name) %>
            <%= icon_tag("check", size: "small", class: "u-ml-narrow") %>
          <% end %>
        </span>
      <% end %>
    <% end %>

    <hr>

    <% %w[asc desc].each do |sort_direction| %>
      <%= link_to url_for(controller: sort_controller, action: :index, **filter_sort_presenter.params(sort: filter_sort_presenter.sort_value || option.default.name, sort_direction: sort_direction)), class: "c-dropdown__item", data: {"turbo-action" => ("replace" if native_app?)} do %>
        <span class='o-flex o-flex--justify-between o-flex--align-center'>
          <%= t("label.#{sort_direction}") %>
          <% if filter_sort_presenter.sort_direction_value == sort_direction || (filter_sort_presenter.sort_direction_value.blank? && option.default.direction == sort_direction) %>
            <%= icon_tag("check", size: "small", class: "u-ml-narrow") %>
          <% end %>
        </span>
      <% end %>
    <% end %>
  </div>
</details>
