<% page_title_tag t("label.add_to_playlist") %>

<% if @playlists.present? %>
  <%= form_tag nil, "data-turbo-frame": "_top", "data-turbo-action": ("replace" if native_app?) do %>
    <%= hidden_field_tag :song_id, params[:song_id] %>

    <%# Because this page in turbo native doesn't render as a turbo frame, %>
    <%# it's render as a whole page in modal in another session. %>
    <%# So we can't get the previous url of the page that open the modal. %>
    <%# And we need a way to save the referer_url as parmas, %>
    <%# so we can redirect to the referer_url later. %>
    <%= hidden_field_tag :referer_url, params[:referer_url] if native_app? %>
    <%= turbo_frame_tag "turbo-dialog-playlists-page-#{@pagy.page}", class: "c-list", target: "_top" do %>
      <%= render partial: "dialog/playlists/playlist", collection: @playlists %>

      <% if @pagy.next %>
        <%= turbo_frame_tag "turbo-dialog-playlists-page-#{@pagy.next}", src: pagy_url_for(@pagy, @pagy.next), loading: "lazy", class: "u-my-small", data: {controller: "element", action: "turbo:frame-render->element#replaceWithChildren"}  do %>
          <div class='o-flex o-flex--justify-center'>
            <%= loader_tag %>
          </div>
        <% end %>
      <% end %>
    <% end %>
  <% end %>
<% else %>
  <%= empty_alert_tag %>
<% end %>
