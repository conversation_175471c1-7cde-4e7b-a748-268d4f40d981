@use "sass:map";
@use "sass:string";
@use "../settings/variables";

@function font-size($type) {
  @return map.get(variables.$font-size, $type);
}

@function spacing($type) {
  @return map.get(variables.$spacing, $type);
}

@function border-radius($type) {
  @return map.get(variables.$border-radius, $type);
}

@function breakpoint-postfix($breakpoint) {
  @if string.length($breakpoint) != 0 {
    @return "\\@#{$breakpoint}";
  }

  @return $breakpoint;
}
