name: Release Build

on:
  push:
    branches:
      - master
    tags:
      - v*

jobs:
  create_release:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Create release
        id: create_release
        if: github.ref_name != 'master'
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ github.ref }}
          release_name: ${{ github.ref }}
          draft: false
          prerelease: ${{ contains(github.ref_name, 'beta') }}

  build:
    needs: create_release
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Get version
        if: github.ref_name != 'master'
        run: |
          # Strip git ref prefix from version
          VERSION=$(echo "${{ github.ref }}" | sed -e 's,.*/\(.*\),\1,')
          # Strip "v" prefix from tag name
          [[ "${{ github.ref }}" == "refs/tags/"* ]] && VERSION=$(echo $VERSION | sed -e 's/^v//')
          echo "VERSION=$VERSION" >> $GITHUB_ENV
      - name: Set Docker Tag
        run: |
          echo "DOCKER_TAG=blackcandy/blackcandy:latest,blackcandy/blackcandy:${{ env.VERSION }},ghcr.io/blackcandy-org/blackcandy:latest,ghcr.io/blackcandy-org/blackcandy:${{ env.VERSION }}" >> $GITHUB_ENV
      - name: Remove Latest Docker Tag
        if: contains(github.ref_name, 'beta')
        run: |
          echo "DOCKER_TAG=blackcandy/blackcandy:${{ env.VERSION }},ghcr.io/blackcandy-org/blackcandy:${{ env.VERSION }}" >> $GITHUB_ENV
      - name: Set Edge Docker Tag
        if: github.ref_name == 'master'
        run: |
          touch .is-edge-release.txt
          echo "DOCKER_TAG=blackcandy/blackcandy:edge,ghcr.io/blackcandy-org/blackcandy:edge" >> $GITHUB_ENV
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}
      - name: Login to GHCR
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.repository_owner }}
          password: ${{ secrets.GITHUB_TOKEN }}
      - name: Build and push
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ./Dockerfile
          platforms: linux/amd64,linux/arm64
          push: true
          tags: ${{ env.DOCKER_TAG }}
          build-args: |
            COMMIT_HASH=${{ github.sha }}
