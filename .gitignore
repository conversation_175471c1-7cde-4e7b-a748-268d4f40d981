# See https://help.github.com/articles/ignoring-files for more about ignoring files.
#
# If you find yourself ignoring temporary files generated by your text editor
# or operating system, you probably want to add a global ignore instead:
#   git config --global core.excludesfile '~/.gitignore_global'

# Ignore bundler config.
/.bundle

# Ignore the default SQLite database.
/db/*.sqlite3
/db/*.sqlite3-*
/db/**/*.sqlite3
/db/**/*.sqlite3-*

# Ignore all logfiles and tempfiles.
/log/*
/tmp/*
!/log/.keep
!/tmp/.keep

/tmp/pids/*
!/tmp/pids/.keep

# Ignore uploaded files in development
/storage/*

/node_modules
/yarn-error.log

/public/assets
.byebug_history

# Ignore master key for decrypting credentials and more.
/config/master.key
/public/packs
/public/packs-test
/public/assets
/node_modules

.DS_Store
*.swp
yarn-debug.log*
.yarn-integrity
.envrc
/.direnv
/.gems
/.pg_data
/.idea
/coverage

/app/assets/builds/*
!/app/assets/builds/.keep

.env
.env.*
!.env.template

.is-edge-release.txt

# Ignore storage (uploaded files in development and any SQLite databases).
/storage/*
!/storage/.keep
/tmp/storage/*
!/tmp/storage/
!/tmp/storage/.keep