{"name": "black_candy", "private": true, "dependencies": {"@hotwired/stimulus": "^3.2.1", "@hotwired/turbo-rails": "^7.3.0", "esbuild": "^0.18.17", "howler": "^2.2.3", "sass": "^1.64.2"}, "devDependencies": {"standard": "^17.1.0", "stylelint": "^15.10.2", "stylelint-config-standard-scss": "^9.0.0"}, "scripts": {"build": "esbuild app/javascript/*.* --bundle --sourcemap --format=esm --outdir=app/assets/builds --public-path=/assets --minify", "build-dev": "esbuild app/javascript/*.* --bundle --sourcemap --format=esm --outdir=app/assets/builds --public-path=/assets", "build:css": "sass app/assets/stylesheets/application.scss:app/assets/builds/application.css --no-source-map --style=compressed", "build-dev:css": "sass app/assets/stylesheets/application.scss:app/assets/builds/application.css --no-source-map"}, "standard": {"globals": ["IntersectionObserver", "MediaMetadata", "Request", "fetch", "CustomEvent", "App"]}}