<div class='o-container o-container--narrow'>
  <div data-controller='songs songs-bridge playlist-bridge' data-playlist-bridge-id-value='<%= @playlist.id %>'>
    <div class="c-card c-card--horizontal u-my-large">
      <div class='c-card__body'>
        <h1 class='c-card__body__title'><%= @playlist.name %></h1>
        <div class='c-card__body__text'>
          <span id='<%= dom_id(@playlist, :songs_count) %>'><%= @playlist.songs.size %></span>
          <span><%= t("label.tracks") %></span>
          <span>,</span>
          <span id='<%= dom_id(@playlist, :songs_duration) %>' class='u-text-monospace'><%= format_duration(@playlist.songs.sum(:duration)) %></span>
        </div>
        <div class='u-mt-large'>
          <% unless @songs.blank? %>
            <%= button_to(
                  t("button.play"),
                  current_playlist_playlist_path(@playlist, should_play: true),
                  method: :put,
                  class: "c-button c-button--primary",
                  form_class: "u-display-inline-block",
                  form: {
                    data: {
                      "disabled-on-native" => "true",
                      "turbo-frame" => "turbo-playlist",
                      "action" => "playlist-bridge#play"
                    }
                  }
                ) %>
            <%= button_to t("button.clear"), favorite_playlist_songs_path, class: "c-button c-button--secondary", form_class: "u-display-inline-block", method: :delete %>
          <% end %>
        </div>
      </div>
    </div>
    <% if @playlist.present? %>
      <%= render partial: "playlists/songs/list", locals: {playlist: @playlist, songs: @songs, pagy: @pagy} %>
    <% end %>
  </div>
</div>
