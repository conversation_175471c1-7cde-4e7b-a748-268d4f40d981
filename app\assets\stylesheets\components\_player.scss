@use "../tools/functions" as *;

.c-player {
  background: var(--player-bg-color);
}

.c-player__control {
  background: var(--player-bg-color);
  position: relative;
  padding: 0 spacing("wide");
  color: var(--player-control-color);
}

.c-player__control__main {
  color: var(--player-control-main-color);
  padding: spacing("small") 0;
  border-bottom: 1px solid var(--player-control-border-color);
}

.c-player__control__secondary {
  padding: spacing("tiny") 0;
}

.c-player__header {
  transition: max-height 0.2s ease-out;
  max-height: 0;
  visibility: hidden;
  background: inherit;
}

.c-player__header__content {
  background: var(--player-header-bg-color);
}

.c-player__header__background {
  position: absolute;
  inset: 0;
  filter: blur(15px);
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}

.c-player__header.is-expanded {
  max-height: 200px;
  visibility: visible;
}

.c-player__progress {
  display: flex;
  align-items: flex-start;
  height: 4px;
}

.c-player__progress progress {
  appearance: none;
  height: 2px;
  border: 0 none;
  background: var(--player-progress-bg-color);
}

.c-player__progress progress::-moz-progress-bar {
  background: var(--player-progress-color);
}

.c-player__progress progress::-webkit-progress-bar {
  background: transparent;
}

.c-player__progress progress::-webkit-progress-value {
  background: var(--player-progress-color);
}

.c-player__progress:hover progress {
  height: 4px;
  background: var(--player-progress-hover-bg-color);
}

.c-player__volume {
  -webkit-appearance: none;
  appearance: none;
  width: 100%;
  cursor: pointer;
  outline: none;
  border-radius: border-radius("large");
  height: 5px;
  background: linear-gradient(to right, var(--player-volume-color) var(--progress), var(--player-volume-bg-color) var(--progress));
}

.c-player__volume::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  height: 12px;
  width: 12px;
  background-color: var(--player-volume-color);
  border-radius: 50%;
  border: none;
}

.c-player__volume::-moz-range-thumb {
  height: 12px;
  width: 12px;
  background-color: var(--player-volume-color);
  border-radius: 50%;
  border: none;
}
