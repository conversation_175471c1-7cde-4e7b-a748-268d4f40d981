@use "../tools/functions" as *;

.c-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.c-list--grouped {
  background-color: var(--list-grouped-bg-color);
  padding: 0 spacing("narrow");
  border-radius: border-radius("large");
}

.c-list__item {
  width: 100%;
  padding: spacing("narrow") 0;
  text-decoration: none;
  border-bottom: 1px solid var(--list-border-color);
}

.c-list__item--divider {
  margin-top: spacing("small");
  font-weight: bold;
  text-transform: uppercase;
}

.c-list__item__subtext {
  display: inline-block;
  margin-top: spacing("tiny");
  color: var(--list-subtext-color);
}

.c-list--border-none .c-list__item {
  border-bottom: none;
}

.c-list--grouped .c-list__item {
  padding: spacing("small") 0;
}

.c-list__item:last-child {
  border-bottom: none;
}

.c-list__item.is-active * {
  color: var(--list-active-color);
}

.c-list.is-dragging .c-list__item {
  border: none;
}

.c-list.is-dragging .c-list__item:not(.is-dragging-source) * {
  // Prevent dragleave from firing when dragging into a child element
  pointer-events: none;
  opacity: 0.85;
}

.c-list__item.is-dragging-over {
  border: 2px dashed var(--list-active-color) !important;
  border-radius: border-radius("medium");
}