sqlite_default: &sqlite_default
  adapter: sqlite3
  pool: <%= ENV.fetch("RAILS_MAX_THREADS") { 5 } %>
  timeout: 5000
  default_transaction_mode: immediate

pg_default: &pg_default
  adapter: postgresql
  encoding: unicode
  pool: <%= ENV.fetch("RAILS_MAX_THREADS") { 5 } %>


<% if BlackCandy.config.db_adapter == "postgresql" %>
development:
  primary: &primary_development
    <<: *pg_default
    username: <%= ENV['DB_USERNAME'] %>
    password: <%= ENV['DB_PASSWORD'] %>
    database: blackcandy_development
    host: localhost
  cache:
    <<: *primary_development
    database: blackcandy_development_cache
    migrations_paths: db/cache_migrate
  cable:
    <<: *primary_development
    database: blackcandy_development_cable
    migrations_paths: db/cable_migrate
  queue:
    <<: *primary_development
    database: blackcandy_development_queue
    migrations_paths: db/queue_migrate

test:
  primary: &primary_test
    <<: *pg_default
    username: <%= ENV['DB_USERNAME'] %>
    password: <%= ENV['DB_PASSWORD'] %>
    database: blackcandy_test
    host: localhost
  cache:
    <<: *primary_test
    database: blackcandy_test_cache
    migrations_paths: db/cache_migrate
  cable:
    <<: *primary_test
    database: blackcandy_test_cable
    migrations_paths: db/cable_migrate
  queue:
    <<: *primary_test
    database: blackcandy_test_queue
    migrations_paths: db/queue_migrate

production:
  primary: &primary_production
    <<: *pg_default
    url: <%= BlackCandy.config.db_url %>
  cache:
    <<: *primary_production
    url: <%= BlackCandy.config.cache_db_url %>
    migrations_paths: db/cache_migrate
  cable:
    <<: *primary_production
    url: <%= BlackCandy.config.cable_db_url %>
    migrations_paths: db/cable_migrate
  queue:
    <<: *primary_production
    url: <%= BlackCandy.config.queue_db_url %>
    migrations_paths: db/queue_migrate
<% else %>
development:
  primary:
    <<: *sqlite_default
    database: storage/development.sqlite3
  cache:
    <<: *sqlite_default
    database: storage/development_cache.sqlite3
    migrations_paths: db/cache_migrate
  cable:
    <<: *sqlite_default
    database: storage/development_cable.sqlite3
    migrations_paths: db/cable_migrate
  queue:
    <<: *sqlite_default
    database: storage/development_queue.sqlite3
    migrations_paths: db/queue_migrate

test:
  primary:
    <<: *sqlite_default
    database: storage/test.sqlite3
  cache:
    <<: *sqlite_default
    database: storage/test_cache.sqlite3
    migrations_paths: db/cache_migrate
  cable:
    <<: *sqlite_default
    database: storage/test_cable.sqlite3
    migrations_paths: db/cable_migrate
  queue:
    <<: *sqlite_default
    database: storage/test_queue.sqlite3
    migrations_paths: db/queue_migrate

production:
  primary:
    <<: *sqlite_default
    database: storage/production.sqlite3
  cache:
    <<: *sqlite_default
    database: storage/production_cache.sqlite3
    migrations_paths: db/cache_migrate
  cable:
    <<: *sqlite_default
    database: storage/production_cable.sqlite3
    migrations_paths: db/cable_migrate
  queue:
    <<: *sqlite_default
    database: storage/production_queue.sqlite3
    migrations_paths: db/queue_migrate
<% end %>
