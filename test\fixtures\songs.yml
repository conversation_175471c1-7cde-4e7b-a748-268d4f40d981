mp3_sample:
  id: 1
  name: 'mp3_sample'
  file_path: <%= Rails.root.join('test', 'fixtures', 'files', 'artist1_album2.mp3') %>
  file_path_hash: 'mp3_sample_file_path_hash'
  md5_hash: 'mp3_sample_md5_hash'
  artist: 'artist1'
  album: 'album2'
  duration: 8.0
  created_at: 2023-01-02

flac_sample:
  id: 2
  name: 'flac_sample'
  file_path: <%= Rails.root.join('test', 'fixtures', 'files', 'artist1_album1.flac') %>
  file_path_hash: 'flac_sample_file_path_hash'
  md5_hash: 'flac_sample_md5_hash'
  artist: 'artist1'
  album: 'album1'
  duration: 8.0,
  bit_depth: 16,
  created_at: 2023-01-01

ogg_sample:
  id: 3
  name: 'ogg_sample'
  file_path: <%= Rails.root.join('test', 'fixtures', 'files', 'artist2_album3.ogg') %>
  file_path_hash: 'ogg_sample_file_path_hash'
  md5_hash: 'ogg_sample_md5_hash'
  artist: 'artist2'
  album: 'album3'
  duration: 8.0
  created_at: 2023-01-03

wav_sample:
  id: 4
  name: 'wav_sample'
  file_path: <%= Rails.root.join('test', 'fixtures', 'files', 'artist2_album3.wav') %>
  file_path_hash: 'wav_sample_file_path_hash'
  md5_hash: 'wav_sample_md5_hash'
  artist: 'artist2'
  album: 'album3'
  duration: 8.0,
  bit_depth: 16,
  created_at: 2023-01-04

opus_sample:
  id: 5
  name: 'opus_sample'
  file_path: <%= Rails.root.join('test', 'fixtures', 'files', 'artist2_album3.opus') %>
  file_path_hash: 'opus_sample_file_path_hash'
  md5_hash: 'opus_sample_md5_hash'
  artist: 'artist2'
  album: 'album3'
  duration: 8.0
  created_at: 2023-01-05

m4a_sample:
  id: 6
  name: 'm4a_sample'
  file_path: <%= Rails.root.join('test', 'fixtures', 'files', 'artist1_album1.m4a') %>
  file_path_hash: 'm4a_sample_file_path_hash'
  md5_hash: 'm4a_sample_md5_hash'
  artist: 'artist1'
  album: 'album1'
  duration: 8.0
  created_at: 2023-01-05

oga_sample:
  id: 7
  name: 'oga_sample'
  file_path: <%= Rails.root.join('test', 'fixtures', 'files', 'artist2_album3.oga') %>
  file_path_hash: 'oga_sample_file_path_hash'
  md5_hash: 'oga_sample_md5_hash'
  artist: 'artist2'
  album: 'album3'
  duration: 8.0
  created_at: 2023-01-05

wma_sample:
  id: 8
  name: 'wma_sample'
  file_path: <%= Rails.root.join('test', 'fixtures', 'files', 'artist2_album3.wma') %>
  file_path_hash: 'wma_sample_file_path_hash'
  md5_hash: 'wma_sample_md5_hash'
  artist: 'artist2'
  album: 'album3'
  duration: 8.0
  bit_depth: 16,
  created_at: 2023-01-06

various_artists_sample:
  id: 9
  name: 'various_artists_sample'
  file_path: <%= Rails.root.join('test', 'fixtures', 'files', 'various_artists.mp3') %>
  file_path_hash: 'various_artists_sample_file_path_hash'
  md5_hash: 'various_artists_sample_md5_hash'
  artist: 'artist1'
  album: 'album4'
  duration: 8.0
  created_at: 2023-01-05
