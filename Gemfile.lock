GEM
  remote: https://rubygems.org/
  specs:
    actioncable (7.2.1)
      actionpack (= 7.2.1)
      activesupport (= 7.2.1)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
      zeitwerk (~> 2.6)
    actionmailbox (7.2.1)
      actionpack (= 7.2.1)
      activejob (= 7.2.1)
      activerecord (= 7.2.1)
      activestorage (= 7.2.1)
      activesupport (= 7.2.1)
      mail (>= 2.8.0)
    actionmailer (7.2.1)
      actionpack (= 7.2.1)
      actionview (= 7.2.1)
      activejob (= 7.2.1)
      activesupport (= 7.2.1)
      mail (>= 2.8.0)
      rails-dom-testing (~> 2.2)
    actionpack (7.2.1)
      actionview (= 7.2.1)
      activesupport (= 7.2.1)
      nokogiri (>= 1.8.5)
      racc
      rack (>= 2.2.4, < 3.2)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
      useragent (~> 0.16)
    actiontext (7.2.1)
      actionpack (= 7.2.1)
      activerecord (= 7.2.1)
      activestorage (= 7.2.1)
      activesupport (= 7.2.1)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (7.2.1)
      activesupport (= 7.2.1)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    activejob (7.2.1)
      activesupport (= 7.2.1)
      globalid (>= 0.3.6)
    activemodel (7.2.1)
      activesupport (= 7.2.1)
    activerecord (7.2.1)
      activemodel (= 7.2.1)
      activesupport (= 7.2.1)
      timeout (>= 0.4.0)
    activestorage (7.2.1)
      actionpack (= 7.2.1)
      activejob (= 7.2.1)
      activerecord (= 7.2.1)
      activesupport (= 7.2.1)
      marcel (~> 1.0)
    activesupport (7.2.1)
      base64
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.3.1)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      securerandom (>= 0.3)
      tzinfo (~> 2.0, >= 2.0.5)
    acts_as_list (1.2.2)
      activerecord (>= 6.1)
      activesupport (>= 6.1)
    addressable (2.8.6)
      public_suffix (>= 2.0.2, < 6.0)
    ast (2.4.2)
    base64 (0.2.0)
    bcrypt (3.1.20)
    bcrypt_pbkdf (1.1.0)
    better_html (2.1.1)
      actionview (>= 6.0)
      activesupport (>= 6.0)
      ast (~> 2.0)
      erubi (~> 1.4)
      parser (>= 2.4)
      smart_properties
    bigdecimal (3.1.8)
    bindex (0.8.1)
    bootsnap (1.18.4)
      msgpack (~> 1.2)
    brakeman (7.0.2)
      racc
    browser (6.0.0)
    builder (3.3.0)
    bullet (7.2.0)
      activesupport (>= 3.0.0)
      uniform_notifier (~> 1.11)
    capybara (3.40.0)
      addressable
      matrix
      mini_mime (>= 0.1.3)
      nokogiri (~> 1.11)
      rack (>= 1.6.0)
      rack-test (>= 0.6.3)
      regexp_parser (>= 1.5, < 3.0)
      xpath (~> 3.2)
    concurrent-ruby (1.3.4)
    connection_pool (2.4.1)
    crack (1.0.0)
      bigdecimal
      rexml
    crass (1.0.6)
    cssbundling-rails (1.4.0)
      railties (>= 6.0.0)
    csv (3.3.0)
    cuprite (0.14.3)
      capybara (~> 3.0)
      ferrum (~> 0.13.0)
    daemons (1.4.1)
    date (3.3.4)
    debug (1.9.2)
      irb (~> 1.10)
      reline (>= 0.3.8)
    docile (1.4.0)
    dotenv (2.8.1)
    drb (2.2.1)
    ed25519 (1.3.0)
    erb_lint (0.4.0)
      activesupport
      better_html (>= 2.0.1)
      parser (>= *******)
      rainbow
      rubocop
      smart_properties
    erubi (1.13.0)
    et-orbi (1.2.11)
      tzinfo
    ferrum (0.13)
      addressable (~> 2.5)
      concurrent-ruby (~> 1.1)
      webrick (~> 1.7)
      websocket-driver (>= 0.6, < 0.8)
    ffi (1.16.3)
    fugit (1.11.1)
      et-orbi (~> 1, >= 1.2.11)
      raabro (~> 1.4)
    globalid (1.2.1)
      activesupport (>= 6.1)
    hashdiff (1.1.0)
    httparty (0.22.0)
      csv
      mini_mime (>= 1.0.0)
      multi_xml (>= 0.5.2)
    i18n (1.14.5)
      concurrent-ruby (~> 1.0)
    image_processing (1.13.0)
      mini_magick (>= 4.9.5, < 5)
      ruby-vips (>= 2.0.17, < 3)
    io-console (0.7.2)
    irb (1.14.0)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    jbuilder (2.13.0)
      actionview (>= 5.0.0)
      activesupport (>= 5.0.0)
    jsbundling-rails (1.3.0)
      railties (>= 6.0.0)
    json (2.7.2)
    kamal (1.4.0)
      activesupport (>= 7.0)
      base64 (~> 0.2)
      bcrypt_pbkdf (~> 1.0)
      concurrent-ruby (~> 1.2)
      dotenv (~> 2.8)
      ed25519 (~> 1.2)
      net-ssh (~> 7.0)
      sshkit (~> 1.21)
      thor (~> 1.2)
      zeitwerk (~> 2.5)
    language_server-protocol (********)
    lint_roller (1.1.0)
    listen (3.9.0)
      rb-fsevent (~> 0.10, >= 0.10.3)
      rb-inotify (~> 0.9, >= 0.9.10)
    logger (1.6.1)
    loofah (2.22.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.4)
    matrix (0.4.2)
    memory_profiler (0.9.14)
    mini_magick (4.13.2)
    mini_mime (1.1.5)
    mini_portile2 (2.8.7)
    minitest (5.25.1)
    msgpack (1.7.2)
    multi_xml (0.7.1)
      bigdecimal (~> 3.1)
    mutex_m (0.2.0)
    net-imap (0.4.16)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-scp (4.0.0)
      net-ssh (>= 2.6.5, < 8.0.0)
    net-sftp (4.0.0)
      net-ssh (>= 5.0.0, < 8.0.0)
    net-smtp (0.5.0)
      net-protocol
    net-ssh (7.2.3)
    nio4r (2.7.3)
    nokogiri (1.16.7)
      mini_portile2 (~> 2.8.2)
      racc (~> 1.4)
    pagy (6.0.4)
    parallel (1.25.1)
    parser (*******)
      ast (~> 2.4.1)
      racc
    pg (1.5.8)
    propshaft (1.1.0)
      actionpack (>= 7.0.0)
      activesupport (>= 7.0.0)
      rack
      railties (>= 7.0.0)
    psych (5.1.2)
      stringio
    public_suffix (5.0.5)
    puma (6.4.3)
      nio4r (~> 2.0)
    raabro (1.4.0)
    racc (1.8.1)
    rack (3.1.7)
    rack-session (2.0.0)
      rack (>= 3.0.0)
    rack-test (2.1.0)
      rack (>= 1.3)
    rackup (2.1.0)
      rack (>= 3)
      webrick (~> 1.8)
    rails (7.2.1)
      actioncable (= 7.2.1)
      actionmailbox (= 7.2.1)
      actionmailer (= 7.2.1)
      actionpack (= 7.2.1)
      actiontext (= 7.2.1)
      actionview (= 7.2.1)
      activejob (= 7.2.1)
      activemodel (= 7.2.1)
      activerecord (= 7.2.1)
      activestorage (= 7.2.1)
      activesupport (= 7.2.1)
      bundler (>= 1.15.0)
      railties (= 7.2.1)
    rails-dom-testing (2.2.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.0)
      loofah (~> 2.21)
      nokogiri (~> 1.14)
    railties (7.2.1)
      actionpack (= 7.2.1)
      activesupport (= 7.2.1)
      irb (~> 1.13)
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      zeitwerk (~> 2.6)
    rainbow (3.1.1)
    rake (13.2.1)
    ransack (4.2.1)
      activerecord (>= 6.1.5)
      activesupport (>= 6.1.5)
      i18n
    rb-fsevent (0.11.2)
    rb-inotify (0.11.1)
      ffi (~> 1.0)
    rdoc (6.7.0)
      psych (>= 4.0.0)
    regexp_parser (2.9.0)
    reline (0.5.10)
      io-console (~> 0.5)
    rexml (3.2.6)
    rubocop (1.48.1)
      json (~> 2.3)
      parallel (~> 1.10)
      parser (>= 3.2.0.0)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 1.8, < 3.0)
      rexml (>= 3.2.5, < 4.0)
      rubocop-ast (>= 1.26.0, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 3.0)
    rubocop-ast (1.31.3)
      parser (>= *******)
    rubocop-performance (1.16.0)
      rubocop (>= 1.7.0, < 2.0)
      rubocop-ast (>= 0.4.0)
    rubocop-rails (2.23.1)
      activesupport (>= 4.2.0)
      rack (>= 1.1)
      rubocop (>= 1.33.0, < 2.0)
      rubocop-ast (>= 1.30.0, < 2.0)
    ruby-progressbar (1.13.0)
    ruby-vips (2.2.2)
      ffi (~> 1.12)
      logger
    securerandom (0.3.1)
    simplecov (0.22.0)
      docile (~> 1.1)
      simplecov-html (~> 0.11)
      simplecov_json_formatter (~> 0.1)
    simplecov-html (0.12.3)
    simplecov-lcov (0.8.0)
    simplecov_json_formatter (0.1.4)
    smart_properties (1.17.0)
    solid_cable (3.0.2)
      actioncable (>= 7.2)
      activejob (>= 7.2)
      activerecord (>= 7.2)
      railties (>= 7.2)
    solid_cache (1.0.6)
      activejob (>= 7.2)
      activerecord (>= 7.2)
      railties (>= 7.2)
    solid_queue (1.0.0)
      activejob (>= 7.1)
      activerecord (>= 7.1)
      concurrent-ruby (>= 1.3.1)
      fugit (~> 1.11.0)
      railties (>= 7.1)
      thor (~> 1.3.1)
    sqlite3 (2.1.0)
      mini_portile2 (~> 2.8.0)
    sshkit (1.22.2)
      base64
      mutex_m
      net-scp (>= 1.1.2)
      net-sftp (>= 2.1.2)
      net-ssh (>= 2.8.0)
    standard (1.25.5)
      language_server-protocol (~> ********)
      rubocop (~> 1.48.1)
      rubocop-performance (~> 1.16.0)
    standard-rails (1.0.2)
      lint_roller (~> 1.0)
      rubocop-rails (~> 2.23.1)
    stimulus-rails (1.3.4)
      railties (>= 6.0.0)
    stringio (3.1.1)
    thor (1.3.2)
    thruster (0.1.8)
    timeout (0.4.1)
    turbo-rails (1.5.0)
      actionpack (>= 6.0.0)
      activejob (>= 6.0.0)
      railties (>= 6.0.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    unicode-display_width (2.5.0)
    uniform_notifier (1.16.0)
    useragent (0.16.10)
    wahwah (1.6.6)
    web-console (4.2.1)
      actionview (>= 6.0.0)
      activemodel (>= 6.0.0)
      bindex (>= 0.4.0)
      railties (>= 6.0.0)
    webmock (3.18.1)
      addressable (>= 2.8.0)
      crack (>= 0.3.2)
      hashdiff (>= 0.4.0, < 2.0.0)
    webrick (1.8.1)
    websocket-driver (0.7.6)
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    xpath (3.2.0)
      nokogiri (~> 1.8)
    zeitwerk (2.6.18)

PLATFORMS
  ruby

DEPENDENCIES
  acts_as_list (~> 1.2.0)
  bcrypt (~> 3.1.11)
  bootsnap (~> 1.18.0)
  brakeman
  browser (~> 6.0.0)
  bullet (~> 7.2.0)
  capybara (~> 3.40.0)
  cssbundling-rails (~> 1.4.0)
  cuprite (~> 0.14.3)
  daemons (~> 1.4.0)
  debug
  erb_lint (~> 0.4.0)
  httparty (~> 0.22.0)
  image_processing (~> 1.13)
  jbuilder (~> 2.13.0)
  jsbundling-rails (~> 1.3.0)
  kamal (~> 1.4.0)
  listen (~> 3.9.0)
  memory_profiler (~> 0.9.13)
  pagy (~> 6.0.0)
  parallel (~> 1.25.0)
  pg (~> 1.5.4)
  propshaft (~> 1.1.0)
  puma (~> 6.4.0)
  rails (~> 7.2.0)
  ransack (~> 4.2.0)
  simplecov (~> 0.22.0)
  simplecov-lcov (~> 0.8.0)
  solid_cable (~> 3.0.0)
  solid_cache (~> 1.0.0)
  solid_queue (~> 1.0.0)
  sqlite3 (~> 2.1.0)
  standard (~> 1.25.0)
  standard-rails
  stimulus-rails (~> 1.3.4)
  thruster (~> 0.1.8)
  turbo-rails (~> 1.5.0)
  tzinfo-data
  wahwah (~> 1.6.0)
  web-console (>= 3.3.0)
  webmock (~> 3.18.0)

BUNDLED WITH
   2.5.9
