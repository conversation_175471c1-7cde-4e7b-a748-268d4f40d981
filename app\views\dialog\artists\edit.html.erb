<% page_title_tag t("label.edit_artist") %>

<%= form_with model: @artist, data: {"turbo-frame" => "_top", "turbo-action" => ("replace" if native_app?)}, class: "c-form" do |form| %>
  <div class='c-form__field'>
    <%= form.label :cover_image, t("field.artist_cover") %>
    <%= form.file_field :cover_image, accept: "image/png, image/jpeg", class: "c-input" %>
  </div>
  <div class='c-form__field c-form__field--submit'>
    <%= form.submit t("button.save"), class: "c-button c-button--primary c-button--full-width" %>
  </div>
<% end %>
