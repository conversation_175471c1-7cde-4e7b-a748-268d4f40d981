<% if filter_sort_presenter.has_filter? %>
  <%= link_to albums_path, class: "c-button c-button--outline c-button--small u-mr-small", data: {"turbo-action" => ("replace" if native_app?)} do %>
    <span class='o-flex o-flex--align-center'>
      <%= icon_tag "close" %>
      <%= t("label.clear_filters") %>
    </span>
  <% end %>
<% end %>

<details class='c-dropdown u-mr-small' data-controller="dropdown">
  <summary class='c-button c-button--outline c-button--small'>
    <span class='o-flex o-flex--align-center'>
      <%= t("field.genre") %>
      <%= icon_tag "expand-more" %>
    </span>
  </summary>
  <div class='c-dropdown__menu c-dropdown__menu--right' data-dropdown-target="menu">
    <%= turbo_frame_tag "turbo-genre-filter", src: albums_filter_genres_path(**filter_sort_presenter.params), loading: "lazy" do %>
      <div class='o-flex o-flex--justify-center u-p-medium'>
        <%= loader_tag size: "small" %>
      </div>
    <% end %>
  </div>
</details>

<details class='c-dropdown u-mr-small' data-controller="dropdown">
  <summary class='c-button c-button--outline c-button--small'>
    <span class='o-flex o-flex--align-center'>
      <%= t("field.year") %>
      <%= icon_tag "expand-more" %>
    </span>
  </summary>
  <div class='c-dropdown__menu' data-dropdown-target="menu">
    <%= turbo_frame_tag "turbo-year-filter", src: albums_filter_years_path(**filter_sort_presenter.params), loading: "lazy" do %>
      <div class='o-flex o-flex--justify-center u-p-medium'>
        <%= loader_tag size: "small" %>
      </div>
    <% end %>
  </div>
</details>
