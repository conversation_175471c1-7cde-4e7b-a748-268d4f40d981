name: CI

on:
  push:
    branches: [ "master" ]
  pull_request:
    branches: [ "master" ]

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  scan_ruby:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: .ruby-version
          bundler-cache: true

      - name: Scan for common Rails security vulnerabilities using static analysis
        run: bin/brakeman --no-pager

  lint:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
      - uses: ruby/setup-ruby@v1
        with:
          ruby-version: .ruby-version
          bundler-cache: true
      - run: bundle install --jobs 4 --retry 3
      - uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'npm'
      - run: npm ci
      - name: Lint
        run: bundle exec rails lint:all

  test:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        database_adapter:
          - postgresql
          - sqlite
    env:
      DB_ADAPTER: ${{ matrix.database_adapter }}
      DB_USERNAME: postgres
      DB_PASSWORD: postgres
    services:
      postgres:
        image: postgres:latest
        ports: ['5432:5432']
        env:
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 3
    steps:
      - uses: actions/checkout@v4
      - uses: ruby/setup-ruby@v1
        with:
          ruby-version: .ruby-version
          bundler-cache: true
      - uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'npm'
      - run: npm ci
      - name: Test
        run: |
          sudo apt-get update
          sudo apt-get -yqq install libpq-dev libvips ffmpeg
          bundle exec rails db:setup
          bundle exec rails test:all

      - name: Push coverage data to Coveralls
        uses: coverallsapp/github-action@v2
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
